import { Tabs } from "@/components/ui/Tabs";
import { useFolderStore } from "@/stores/useFolderStore";
import { OctagonX } from "lucide-react";

export const NoteTabs = () => {
  const folderStore = useFolderStore();

  const activeValue = folderStore.activeNote?.id || folderStore.recentNotes[0]?.id || "";

  const handleTabChange = (value: string) => {
    folderStore.setActiveNote(value);
  };

  const handleCloseTab = (noteId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    folderStore.closeNote(noteId);
  };

  if (folderStore.recentNotes.length === 0) {
    return null;
  }

  return (
    <Tabs value={activeValue} onValueChange={handleTabChange}>
      <Tabs.List>
        {folderStore.recentNotes.map((note) => (
          <Tabs.Trigger
            key={note.id}
            value={note.id}
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            onAuxClick={(e) => {
              if (e.button === 1) {
                handleCloseTab(note.id, e);
              }
            }}
          >
            {note.title || "Untitled"}
            <OctagonX className="ml-2 cursor-pointer" onClick={(e) => handleCloseTab(note.id, e)} />
          </Tabs.Trigger>
        ))}
      </Tabs.List>
    </Tabs>
  );
};
