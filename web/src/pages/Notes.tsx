import { NoteTabs } from "@/components/features/notes/NoteTabs";
import { useFolderStore } from "@/stores/useFolderStore";
import { useEffect } from "react";
import { useNavigate } from "react-router";

export const Notes = () => {
  const folderStore = useFolderStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (folderStore.recentNotes.length === 0) {
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [folderStore.recentNotes]);

  return (
    <div className="max-w-2xl">
      <NoteTabs />
      <div className="ml-1 mt-4">
        <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight text-balance">
          {folderStore.activeNote?.title}
        </h1>
      </div>
    </div>
  );
};
